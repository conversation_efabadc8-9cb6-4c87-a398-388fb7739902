

import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "STA"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "j_score"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "j_score"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "j_score"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "j_score"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))




import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
# 输出文件A列名为x的列和文件B列名为y的列的Pearson、Spearman
print_data = []
gloden_file = (
    "../../../TDE/EN.tsv"
)

model_file_output_dir = "./results/"
model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(".csv")]
model_file_outputs = sorted(model_file_outputs)
column_name = "j_score"

print_data = []

for model_file_output in model_file_outputs:
    print(model_file_output)
    model_file_output = model_file_output_dir + model_file_output
    # 读取文件
    df_gloden = pd.read_csv(gloden_file, sep='\t')
    df_model = pd.read_csv(model_file_output)
    # 提取需要的列
    col_gloden = df_gloden[column_name]
    col_model = df_model[column_name]

    # 计算各项指标
    pearson = pearsonr(col_gloden, col_model)[0]
    spearman = spearmanr(col_gloden, col_model)[0]
    print_data.append([model_file_output, pearson, spearman])

# 输出为markdown格式 按照 data[1] 降序排列
# 按照 data[1] 降序排列
print_data.sort(key=lambda x: x[1], reverse=True)

print("| Prompt | Pearson | Spearman |")
print("| --- | --- | --- |")
for data in print_data:
    print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))
